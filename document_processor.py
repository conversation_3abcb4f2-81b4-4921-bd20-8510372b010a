import pytesseract
from PIL import Image
import cv2
import numpy as np
import re
import os
from document_types import DocumentTypeManager

class DocumentProcessor:
    def __init__(self):
        self.doc_type_manager = DocumentTypeManager()
        # Configure tesseract path if needed (Windows)
        # pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    
    def preprocess_image(self, image_path):
        """Preprocess image for better OCR results"""
        # Read image
        img = cv2.imread(image_path)
        
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Apply noise reduction
        denoised = cv2.medianBlur(gray, 3)
        
        # Apply threshold to get image with only black and white
        _, thresh = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Morphological operations to remove noise
        kernel = np.ones((1, 1), np.uint8)
        processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        processed = cv2.morphologyEx(processed, cv2.MORPH_OPEN, kernel)
        
        return processed
    
    def extract_text_from_image(self, image_path):
        """Extract text from image using Tesseract OCR"""
        try:
            # Preprocess image
            processed_img = self.preprocess_image(image_path)
            
            # Convert back to PIL Image
            pil_img = Image.fromarray(processed_img)
            
            # Extract text using Tesseract
            text = pytesseract.image_to_string(pil_img, lang='eng')
            
            return text.strip()
        except Exception as e:
            print(f"Error extracting text from {image_path}: {str(e)}")
            return ""
    
    def identify_document_type(self, text):
        """Identify document type using the document type manager"""
        doc_type, confidence = self.doc_type_manager.identify_document_type(text)
        return doc_type
    
    def extract_structured_data(self, text, doc_type):
        """Extract structured data using document type manager"""
        # Use the document type manager for extraction
        structured_data = self.doc_type_manager.extract_structured_data(text, doc_type)

        # Fallback to legacy methods if needed
        if not structured_data and doc_type != 'Unknown':
            if doc_type == 'PAN Card':
                structured_data = self.extract_pan_data(text)
            elif doc_type == 'Aadhaar Card':
                structured_data = self.extract_aadhaar_data(text)
            elif doc_type == 'Driving License':
                structured_data = self.extract_dl_data(text)
            elif doc_type == 'Passport':
                structured_data = self.extract_passport_data(text)
            elif doc_type == 'Voter ID':
                structured_data = self.extract_voter_data(text)

        return structured_data
    
    def extract_pan_data(self, text):
        """Extract PAN card specific data"""
        data = {}
        
        # PAN Number
        pan_match = re.search(r'([A-Z]{5}[0-9]{4}[A-Z])', text)
        if pan_match:
            data['PAN Number'] = pan_match.group(1)
        
        # Name (usually appears after "Name" or before "Father's Name")
        name_patterns = [
            r'Name[:\s]*([A-Z\s]+?)(?:\n|Father)',
            r'^([A-Z\s]+?)(?:\n.*Father)',
        ]
        for pattern in name_patterns:
            name_match = re.search(pattern, text, re.MULTILINE | re.IGNORECASE)
            if name_match:
                name = name_match.group(1).strip()
                if len(name) > 2 and not any(word in name.lower() for word in ['income', 'tax', 'department']):
                    data['Name'] = name
                    break
        
        # Father's Name
        father_patterns = [
            r'Father[\'s]*\s*Name[:\s]*([A-Z\s]+?)(?:\n|$)',
            r'Father[\'s]*[:\s]*([A-Z\s]+?)(?:\n|$)',
        ]
        for pattern in father_patterns:
            father_match = re.search(pattern, text, re.IGNORECASE)
            if father_match:
                data['Father\'s Name'] = father_match.group(1).strip()
                break
        
        # Date of Birth
        dob_patterns = [
            r'(\d{2}[/-]\d{2}[/-]\d{4})',
            r'(\d{2}\.\d{2}\.\d{4})',
        ]
        for pattern in dob_patterns:
            dob_match = re.search(pattern, text)
            if dob_match:
                data['Date of Birth'] = dob_match.group(1)
                break
        
        return data
    
    def extract_aadhaar_data(self, text):
        """Extract Aadhaar card specific data"""
        data = {}
        
        # Aadhaar Number
        aadhaar_match = re.search(r'(\d{4}\s\d{4}\s\d{4})', text)
        if aadhaar_match:
            data['Aadhaar Number'] = aadhaar_match.group(1)
        
        # Name (usually the largest text or after specific keywords)
        name_patterns = [
            r'(?:Name[:\s]*)?([A-Z][a-z]+(?:\s[A-Z][a-z]+)*)',
            r'^([A-Z][a-z]+(?:\s[A-Z][a-z]+)+)',
        ]
        for pattern in name_patterns:
            matches = re.findall(pattern, text, re.MULTILINE)
            for match in matches:
                if len(match) > 5 and not any(word in match.lower() for word in ['authority', 'india', 'government']):
                    data['Name'] = match
                    break
            if 'Name' in data:
                break
        
        # Date of Birth
        dob_patterns = [
            r'DOB[:\s]*(\d{2}[/-]\d{2}[/-]\d{4})',
            r'Birth[:\s]*(\d{2}[/-]\d{2}[/-]\d{4})',
            r'(\d{2}[/-]\d{2}[/-]\d{4})',
        ]
        for pattern in dob_patterns:
            dob_match = re.search(pattern, text, re.IGNORECASE)
            if dob_match:
                data['Date of Birth'] = dob_match.group(1)
                break
        
        # Gender
        if re.search(r'\bmale\b', text, re.IGNORECASE):
            data['Gender'] = 'Male'
        elif re.search(r'\bfemale\b', text, re.IGNORECASE):
            data['Gender'] = 'Female'
        
        # Address
        address_match = re.search(r'Address[:\s]*(.+?)(?:\n\n|\n[A-Z]|\d{4}\s\d{4})', text, re.IGNORECASE | re.DOTALL)
        if address_match:
            data['Address'] = address_match.group(1).strip()
        
        return data

    def extract_dl_data(self, text):
        """Extract Driving License specific data"""
        data = {}

        # License Number
        dl_patterns = [
            r'DL[:\s]*([A-Z0-9]+)',
            r'License[:\s]*([A-Z0-9]+)',
        ]
        for pattern in dl_patterns:
            dl_match = re.search(pattern, text, re.IGNORECASE)
            if dl_match:
                data['License Number'] = dl_match.group(1)
                break

        # Name
        name_patterns = [
            r'Name[:\s]*([A-Z\s]+?)(?:\n|$)',
            r'^([A-Z][a-z]+(?:\s[A-Z][a-z]+)+)',
        ]
        for pattern in name_patterns:
            name_match = re.search(pattern, text, re.MULTILINE | re.IGNORECASE)
            if name_match:
                data['Name'] = name_match.group(1).strip()
                break

        return data

    def extract_passport_data(self, text):
        """Extract Passport specific data"""
        data = {}

        # Passport Number
        passport_match = re.search(r'([A-Z]\d{7})', text)
        if passport_match:
            data['Passport Number'] = passport_match.group(1)

        return data

    def extract_voter_data(self, text):
        """Extract Voter ID specific data"""
        data = {}

        # Voter ID Number
        voter_patterns = [
            r'([A-Z]{3}\d{7})',
            r'EPIC[:\s]*([A-Z0-9]+)',
        ]
        for pattern in voter_patterns:
            voter_match = re.search(pattern, text)
            if voter_match:
                data['Voter ID'] = voter_match.group(1)
                break

        return data

    def process_document(self, image_path, filename):
        """Main method to process a document"""
        # Extract text
        raw_text = self.extract_text_from_image(image_path)

        # Identify document type
        doc_type = self.identify_document_type(raw_text)

        # Create base result
        result = {
            'filename': filename,
            'Document Type': doc_type,
            'Raw Text': raw_text
        }

        # Extract structured data if document type is recognized
        if doc_type != 'Unknown':
            structured_data = self.extract_structured_data(raw_text, doc_type)
            result.update(structured_data)

            # Learn from this document for future improvements
            self.doc_type_manager.learn_from_document(filename, doc_type, structured_data, raw_text)

        return result
