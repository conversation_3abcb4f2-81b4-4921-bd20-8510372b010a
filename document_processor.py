import pytesseract
from PIL import Image
import cv2
import numpy as np
import re
import os
from document_types import DocumentTypeManager

class DocumentProcessor:
    def __init__(self):
        self.doc_type_manager = DocumentTypeManager()
        # Configure tesseract path for Windows
        import platform
        if platform.system() == 'Windows':
            # Common Tesseract installation paths on Windows
            possible_paths = [
                r'C:\Program Files\Tesseract-OCR\tesseract.exe',
                r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
                r'C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe'.format(os.environ.get('USERNAME', '')),
                r'C:\tesseract\tesseract.exe'
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
                    print(f"Found Tesseract at: {path}")
                    break
            else:
                print("Warning: Tesseract not found in common locations. Please set the path manually.")
    
    def preprocess_image(self, image_path):
        """Preprocess image for better OCR results with Indian documents"""
        # Read image
        img = cv2.imread(image_path)

        # Resize image if too small (helps with OCR accuracy)
        height, width = img.shape[:2]
        if height < 600 or width < 800:
            scale_factor = max(800/width, 600/height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization) for better contrast
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)

        # Apply noise reduction
        denoised = cv2.medianBlur(enhanced, 3)

        # Apply Gaussian blur to smooth the image
        blurred = cv2.GaussianBlur(denoised, (1, 1), 0)

        # Apply threshold to get image with only black and white
        _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # Morphological operations to remove noise and connect text
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

        # Remove small noise
        kernel_open = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 1))
        processed = cv2.morphologyEx(processed, cv2.MORPH_OPEN, kernel_open)

        return processed
    
    def extract_text_from_image(self, image_path):
        """Extract text from image using Tesseract OCR with multi-language support"""
        try:
            # Preprocess image
            processed_img = self.preprocess_image(image_path)

            # Convert back to PIL Image
            pil_img = Image.fromarray(processed_img)

            # Extract text using Tesseract - English only for consistency
            best_text = ""

            # Try with preprocessed image first
            try:
                best_text = pytesseract.image_to_string(pil_img, lang='eng', config='--psm 6')
                print(f"OCR result: {len(best_text.strip())} characters extracted")
            except Exception as e:
                print(f"Primary OCR failed: {e}")

            # If no good result, try with original image
            if not best_text.strip():
                try:
                    original_img = Image.open(image_path)
                    best_text = pytesseract.image_to_string(original_img, lang='eng', config='--psm 3')
                    print("Used original image without preprocessing")
                except Exception as e:
                    print(f"Fallback OCR failed: {e}")

            # Try different PSM mode if still not enough text
            if len(best_text.strip()) < 30:
                try:
                    alt_text = pytesseract.image_to_string(pil_img, lang='eng', config='--psm 8')
                    if len(alt_text.strip()) > len(best_text.strip()):
                        best_text = alt_text
                        print("Used alternative PSM mode for better results")
                except:
                    pass

            return best_text.strip()

        except Exception as e:
            print(f"Error extracting text from {image_path}: {str(e)}")
            return ""
    
    def identify_document_type(self, text):
        """Identify document type using the document type manager"""
        doc_type, confidence = self.doc_type_manager.identify_document_type(text)
        return doc_type
    
    def extract_structured_data(self, text, doc_type):
        """Extract structured data using document type manager"""
        # Use the document type manager for extraction
        structured_data = self.doc_type_manager.extract_structured_data(text, doc_type)

        # Fallback to legacy methods if needed
        if not structured_data and doc_type != 'Unknown':
            if doc_type == 'PAN Card':
                structured_data = self.extract_pan_data(text)
            elif doc_type == 'Aadhaar Card':
                structured_data = self.extract_aadhaar_data(text)
            elif doc_type == 'Driving License':
                structured_data = self.extract_dl_data(text)
            elif doc_type == 'Passport':
                structured_data = self.extract_passport_data(text)
            elif doc_type == 'Voter ID':
                structured_data = self.extract_voter_data(text)

        return structured_data
    
    def extract_pan_data(self, text):
        """Extract PAN card specific data with improved accuracy"""
        data = {}

        # Clean the text
        lines = [line.strip() for line in text.split('\n') if line.strip()]

        # PAN Number - more robust pattern
        pan_patterns = [
            r'([A-Z]{5}[0-9]{4}[A-Z])',  # Standard PAN format
            r'PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z])',  # With PAN prefix
        ]
        for pattern in pan_patterns:
            pan_match = re.search(pattern, text)
            if pan_match:
                data['PAN Number'] = pan_match.group(1) if 'PAN' not in pattern else pan_match.group(1)
                break

        # Name extraction - improved for PAN cards
        name_candidates = []

        # Look for names in specific patterns - English only
        name_patterns = [
            r'(?:Name)[:\s]*([A-Z][A-Z\s]+?)(?:\n|Father)',
            r'^([A-Z][A-Z\s]+?)(?:\n.*(?:Father))',
        ]

        for pattern in name_patterns:
            name_match = re.search(pattern, text, re.MULTILINE | re.IGNORECASE)
            if name_match:
                name = name_match.group(1).strip()
                # Clean and validate
                name = re.sub(r'\s+', ' ', name)
                if self._is_valid_pan_name(name):
                    name_candidates.append(name)

        # Also check lines that look like names (all caps, 2-4 words)
        for line in lines:
            if re.match(r'^[A-Z][A-Z\s]+$', line) and len(line.split()) >= 2:
                if self._is_valid_pan_name(line):
                    name_candidates.append(line)

        if name_candidates:
            # Prefer shorter, cleaner names
            best_name = min(name_candidates, key=len)
            data['Name'] = best_name

        # Father's Name - English only
        father_patterns = [
            r'(?:Father[\'s]*\s*Name)[:\s]*([A-Z][A-Z\s]+?)(?:\n|$)',
            r'(?:Father)[\'s]*[:\s]*([A-Z][A-Z\s]+?)(?:\n|$)',
        ]
        for pattern in father_patterns:
            father_match = re.search(pattern, text, re.IGNORECASE)
            if father_match:
                father_name = father_match.group(1).strip()
                father_name = re.sub(r'\s+', ' ', father_name)
                if self._is_valid_pan_name(father_name):
                    data['Father\'s Name'] = father_name
                    break

        # Date of Birth - English only
        dob_patterns = [
            r'(?:DOB|Birth)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            r'(\d{1,2}\.\d{1,2}\.\d{4})',
        ]
        for pattern in dob_patterns:
            dob_match = re.search(pattern, text, re.IGNORECASE)
            if dob_match:
                data['Date of Birth'] = dob_match.group(1)
                break

        return data

    def _is_valid_pan_name(self, name):
        """Check if a string looks like a valid PAN card name"""
        if not name or len(name) < 3:
            return False

        # PAN names are usually all caps
        if not name.isupper():
            return False

        words = name.split()
        if len(words) < 2 or len(words) > 5:
            return False

        # Check for invalid content
        invalid_words = ['INCOME', 'TAX', 'DEPARTMENT', 'GOVT', 'INDIA', 'PERMANENT', 'ACCOUNT', 'NUMBER']
        if any(word in invalid_words for word in words):
            return False

        # Check for numbers or special characters
        if re.search(r'[0-9]', name) or re.search(r'[^\w\s]', name):
            return False

        return True
    
    def extract_aadhaar_data(self, text):
        """Extract Aadhaar card specific data with improved pattern matching"""
        data = {}

        # Clean the text for better processing
        lines = [line.strip() for line in text.split('\n') if line.strip()]

        # Aadhaar Number - multiple patterns
        aadhaar_patterns = [
            r'(\d{4}\s+\d{4}\s+\d{4})',  # Standard format with spaces
            r'(\d{4}\s*\d{4}\s*\d{4})',  # With or without spaces
            r'(\d{12})',                  # 12 digits together
        ]
        for pattern in aadhaar_patterns:
            aadhaar_match = re.search(pattern, text)
            if aadhaar_match:
                number = aadhaar_match.group(1)
                # Format with spaces if needed
                if len(number) == 12 and ' ' not in number:
                    number = f"{number[:4]} {number[4:8]} {number[8:]}"
                data['Aadhaar Number'] = number
                break

        # Name extraction - improved logic for different Aadhaar formats
        name_candidates = []

        # Look for names in specific patterns - English only
        name_patterns = [
            # Pattern 1: After Name: keyword
            r'(?:name)[:\s]*([A-Z][a-z]+(?:\s+[A-Z][a-z]+){1,3})',
            # Pattern 2: Standalone name lines (2-4 words, proper case)
            r'^([A-Z][a-z]+(?:\s+[A-Z][a-z]+){1,3})$',
            # Pattern 3: After government headers, look for next proper name
            r'(?:government of india|aadhaar).*?\n(?:[^\n]*\n)*?\s*([A-Z][a-z]+(?:\s+[A-Z][a-z]+){1,3})\s*\n',
        ]

        for pattern in name_patterns:
            matches = re.findall(pattern, text, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                # Clean and validate the name
                clean_name = re.sub(r'[^\w\s]', '', match).strip()
                if self._is_valid_name(clean_name):
                    name_candidates.append(clean_name)

        # Also check individual lines for names
        for i, line in enumerate(lines):
            line = line.strip()

            # Skip lines with common non-name content
            skip_words = ['government', 'india', 'aadhaar', 'authority', 'dob', 'address', 'male', 'female', 'year', 'unique', 'identification']
            if any(skip_word in line.lower() for skip_word in skip_words):
                continue

            # Skip lines that are too short or contain numbers/special chars
            if len(line) < 5 or re.search(r'[0-9:/]', line):
                continue

            # Check if line looks like a name (2-4 words, proper case)
            if re.match(r'^[A-Z][a-z]+(?:\s+[A-Z][a-z]+){1,3}$', line):
                if self._is_valid_name(line):
                    # Give higher priority to names that appear after headers
                    if i > 0 and any(header in lines[i-1].lower() for header in ['aadhaar', 'आधार']):
                        name_candidates.insert(0, line)  # Add to front
                    else:
                        name_candidates.append(line)

        # Select the best name candidate
        if name_candidates:
            # Prefer names that are 2-3 words long
            best_name = min(name_candidates, key=lambda x: abs(len(x.split()) - 2.5))
            data['Name'] = best_name

        # Date of Birth - multiple formats, English only
        dob_patterns = [
            r'(?:DOB|Birth)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            r'(\d{1,2}\.\d{1,2}\.\d{4})',
        ]
        for pattern in dob_patterns:
            dob_match = re.search(pattern, text, re.IGNORECASE)
            if dob_match:
                data['Date of Birth'] = dob_match.group(1)
                break

        # Gender - English only
        gender_patterns = [
            r'\b(male|female)\b',
            r'(?:gender)[:\s]*(male|female)',
        ]
        for pattern in gender_patterns:
            gender_match = re.search(pattern, text, re.IGNORECASE)
            if gender_match:
                gender = gender_match.group(1).lower()
                if gender == 'male':
                    data['Gender'] = 'Male'
                elif gender == 'female':
                    data['Gender'] = 'Female'
                break

        # Address - improved extraction
        address_patterns = [
            r'(?:Address|पता)[:\s]*(.+?)(?:\n\n|\n[A-Z]|\d{4}\s*\d{4})',
            r'(?:S/O|D/O|W/O)[:\s]*[^,\n]+,\s*(.+?)(?:\n\n|\n[A-Z])',
        ]
        for pattern in address_patterns:
            address_match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if address_match:
                address = address_match.group(1).strip()
                # Clean up the address
                address = re.sub(r'\s+', ' ', address)
                if len(address) > 10:  # Only if we got a reasonable address
                    data['Address'] = address
                    break

        return data

    def _is_valid_name(self, name):
        """Check if a string looks like a valid person name"""
        if not name or len(name) < 3:
            return False

        words = name.split()
        if len(words) < 2 or len(words) > 4:
            return False

        # Check if all words start with capital letter
        if not all(word[0].isupper() for word in words):
            return False

        # Check for common non-name patterns
        invalid_patterns = [
            r'\d',  # Contains numbers
            r'[^\w\s]',  # Contains special characters
        ]

        for pattern in invalid_patterns:
            if re.search(pattern, name):
                return False

        # Check for invalid words that shouldn't be in names
        invalid_words = ['GOVERNMENT', 'INDIA', 'AADHAAR', 'AUTHORITY', 'UNIQUE', 'IDENTIFICATION', 'CARD', 'DOB', 'ADDRESS']
        name_upper = name.upper()
        if any(word in name_upper for word in invalid_words):
            return False

        # Check if words are reasonable length (not too short or too long)
        if any(len(word) < 2 or len(word) > 15 for word in words):
            return False

        return True

    def extract_dl_data(self, text):
        """Extract Driving License specific data"""
        data = {}

        # License Number
        dl_patterns = [
            r'DL[:\s]*([A-Z0-9]+)',
            r'License[:\s]*([A-Z0-9]+)',
        ]
        for pattern in dl_patterns:
            dl_match = re.search(pattern, text, re.IGNORECASE)
            if dl_match:
                data['License Number'] = dl_match.group(1)
                break

        # Name
        name_patterns = [
            r'Name[:\s]*([A-Z\s]+?)(?:\n|$)',
            r'^([A-Z][a-z]+(?:\s[A-Z][a-z]+)+)',
        ]
        for pattern in name_patterns:
            name_match = re.search(pattern, text, re.MULTILINE | re.IGNORECASE)
            if name_match:
                data['Name'] = name_match.group(1).strip()
                break

        return data

    def extract_passport_data(self, text):
        """Extract Passport specific data"""
        data = {}

        # Passport Number
        passport_match = re.search(r'([A-Z]\d{7})', text)
        if passport_match:
            data['Passport Number'] = passport_match.group(1)

        return data

    def extract_voter_data(self, text):
        """Extract Voter ID specific data"""
        data = {}

        # Voter ID Number
        voter_patterns = [
            r'([A-Z]{3}\d{7})',
            r'EPIC[:\s]*([A-Z0-9]+)',
        ]
        for pattern in voter_patterns:
            voter_match = re.search(pattern, text)
            if voter_match:
                data['Voter ID'] = voter_match.group(1)
                break

        return data

    def process_document(self, image_path, filename):
        """Main method to process a document"""
        # Extract text
        raw_text = self.extract_text_from_image(image_path)

        # Identify document type
        doc_type = self.identify_document_type(raw_text)

        # Create base result
        result = {
            'filename': filename,
            'Document Type': doc_type,
            'Raw Text': raw_text
        }

        # Extract structured data if document type is recognized
        if doc_type != 'Unknown':
            structured_data = self.extract_structured_data(raw_text, doc_type)
            result.update(structured_data)

            # Learn from this document for future improvements
            self.doc_type_manager.learn_from_document(filename, doc_type, structured_data, raw_text)

        return result
