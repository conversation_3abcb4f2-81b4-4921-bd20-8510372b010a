from flask import Flask, request, render_template, jsonify, redirect, url_for
import os
import json
from werkzeug.utils import secure_filename
from document_processor import DocumentProcessor
from document_types import DocumentTypeManager

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Initialize processors
document_processor = DocumentProcessor()
doc_type_manager = DocumentTypeManager()

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff', 'pdf'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_files():
    if 'files' not in request.files:
        return jsonify({'error': 'No files selected'}), 400
    
    files = request.files.getlist('files')
    if not files or all(file.filename == '' for file in files):
        return jsonify({'error': 'No files selected'}), 400
    
    results = []
    
    for file in files:
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            # Process the document
            result = document_processor.process_document(filepath, filename)
            results.append(result)
    
    # Group results by person/entity
    grouped_results = group_documents_by_person(results)
    
    return jsonify(grouped_results)

@app.route('/document-types')
def get_document_types():
    return jsonify(doc_type_manager.get_all_types())

@app.route('/add-document-type', methods=['POST'])
def add_document_type():
    data = request.get_json()
    doc_type_manager.add_new_type(data['name'], data['patterns'])
    return jsonify({'success': True})

@app.route('/learn-document', methods=['POST'])
def learn_document():
    data = request.get_json()
    filename = data['filename']
    doc_type = data['document_type']
    extracted_data = data['extracted_data']
    
    # Learn from this document for future recognition
    doc_type_manager.learn_from_document(filename, doc_type, extracted_data)
    
    return jsonify({'success': True})

def group_documents_by_person(results):
    """Group documents by person/entity name"""
    grouped = {}
    
    for result in results:
        # Try to find a person identifier (name)
        person_key = "Unknown"
        
        if result.get('Document Type') != 'Unknown':
            # Look for name fields
            if 'Name' in result:
                person_key = result['Name']
            elif 'Father\'s Name' in result:
                person_key = result['Father\'s Name']
        
        if person_key not in grouped:
            grouped[person_key] = {
                "documents": []
            }
        
        grouped[person_key]["documents"].append(result)
    
    # Convert to the required format
    formatted_result = []
    for person, data in grouped.items():
        formatted_result.append({person: data})
    
    return formatted_result

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
