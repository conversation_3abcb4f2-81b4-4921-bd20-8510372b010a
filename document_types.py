import json
import os
import re
from datetime import datetime

class DocumentTypeManager:
    def __init__(self, config_file='document_types.json'):
        self.config_file = config_file
        self.document_types = self.load_document_types()
        self.learning_data = self.load_learning_data()
    
    def load_document_types(self):
        """Load document type configurations"""
        default_types = {
            'PAN Card': {
                'keywords': ['income tax', 'permanent account', 'pan'],
                'patterns': {
                    'PAN Number': r'([A-Z]{5}[0-9]{4}[A-Z])',
                    'Name': r'Name[:\s]*([A-Z\s]+?)(?:\n|Father)',
                    'Father\'s Name': r'Father[\'s]*\s*Name[:\s]*([A-Z\s]+?)(?:\n|$)',
                    'Date of Birth': r'(\d{2}[/-]\d{2}[/-]\d{4})'
                },
                'confidence_threshold': 0.7
            },
            'Aadhaar Card': {
                'keywords': ['aadhaar', 'aadhar', 'unique identification', 'uidai'],
                'patterns': {
                    'Aadhaar Number': r'(\d{4}\s\d{4}\s\d{4})',
                    'Name': r'(?:Name[:\s]*)?([A-Z][a-z]+(?:\s[A-Z][a-z]+)*)',
                    'Date of Birth': r'DOB[:\s]*(\d{2}[/-]\d{2}[/-]\d{4})',
                    'Gender': r'\b(male|female)\b',
                    'Address': r'Address[:\s]*(.+?)(?:\n\n|\n[A-Z]|\d{4}\s\d{4})'
                },
                'confidence_threshold': 0.7
            },
            'Driving License': {
                'keywords': ['driving license', 'driving licence', 'dl no'],
                'patterns': {
                    'License Number': r'DL[:\s]*([A-Z0-9]+)',
                    'Name': r'Name[:\s]*([A-Z\s]+?)(?:\n|$)'
                },
                'confidence_threshold': 0.6
            },
            'Passport': {
                'keywords': ['passport', 'republic of india'],
                'patterns': {
                    'Passport Number': r'([A-Z]\d{7})',
                    'Name': r'Name[:\s]*([A-Z\s]+?)(?:\n|$)'
                },
                'confidence_threshold': 0.6
            },
            'Voter ID': {
                'keywords': ['election commission', 'voter', 'epic'],
                'patterns': {
                    'Voter ID': r'([A-Z]{3}\d{7})',
                    'Name': r'Name[:\s]*([A-Z\s]+?)(?:\n|$)'
                },
                'confidence_threshold': 0.6
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    loaded_types = json.load(f)
                    # Merge with defaults
                    default_types.update(loaded_types)
            except Exception as e:
                print(f"Error loading document types: {e}")
        
        return default_types
    
    def load_learning_data(self):
        """Load learning data for document recognition improvement"""
        learning_file = 'learning_data.json'
        if os.path.exists(learning_file):
            try:
                with open(learning_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading learning data: {e}")
        
        return {'document_samples': [], 'pattern_improvements': {}}
    
    def save_document_types(self):
        """Save document type configurations"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.document_types, f, indent=2)
        except Exception as e:
            print(f"Error saving document types: {e}")
    
    def save_learning_data(self):
        """Save learning data"""
        try:
            with open('learning_data.json', 'w') as f:
                json.dump(self.learning_data, f, indent=2)
        except Exception as e:
            print(f"Error saving learning data: {e}")
    
    def identify_document_type(self, text):
        """Identify document type with confidence scoring"""
        text_lower = text.lower()
        scores = {}
        
        for doc_type, config in self.document_types.items():
            score = 0
            keyword_matches = 0
            pattern_matches = 0
            
            # Check keywords
            for keyword in config['keywords']:
                if keyword in text_lower:
                    keyword_matches += 1
            
            # Check patterns
            for field, pattern in config['patterns'].items():
                if re.search(pattern, text, re.IGNORECASE):
                    pattern_matches += 1
            
            # Calculate score
            keyword_score = keyword_matches / len(config['keywords']) if config['keywords'] else 0
            pattern_score = pattern_matches / len(config['patterns']) if config['patterns'] else 0
            
            # Weighted average (keywords 40%, patterns 60%)
            score = (keyword_score * 0.4) + (pattern_score * 0.6)
            scores[doc_type] = score
        
        # Find best match
        best_type = max(scores, key=scores.get) if scores else 'Unknown'
        best_score = scores.get(best_type, 0)
        
        # Check if confidence threshold is met
        threshold = self.document_types.get(best_type, {}).get('confidence_threshold', 0.5)
        if best_score >= threshold:
            return best_type, best_score
        
        return 'Unknown', 0
    
    def extract_structured_data(self, text, doc_type):
        """Extract structured data using configured patterns"""
        if doc_type not in self.document_types:
            return {}
        
        data = {}
        patterns = self.document_types[doc_type]['patterns']
        
        for field, pattern in patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
            if matches:
                # Take the first match, clean it up
                value = matches[0].strip() if isinstance(matches[0], str) else matches[0]
                if value:
                    data[field] = value
        
        return data
    
    def add_new_type(self, type_name, keywords, patterns, confidence_threshold=0.6):
        """Add a new document type"""
        self.document_types[type_name] = {
            'keywords': keywords,
            'patterns': patterns,
            'confidence_threshold': confidence_threshold,
            'created_date': datetime.now().isoformat()
        }
        self.save_document_types()
    
    def learn_from_document(self, filename, doc_type, extracted_data, raw_text):
        """Learn from a processed document to improve recognition"""
        sample = {
            'filename': filename,
            'document_type': doc_type,
            'extracted_data': extracted_data,
            'raw_text': raw_text,
            'timestamp': datetime.now().isoformat()
        }
        
        self.learning_data['document_samples'].append(sample)
        
        # Analyze patterns for improvement
        self._analyze_patterns(doc_type, extracted_data, raw_text)
        
        self.save_learning_data()
    
    def _analyze_patterns(self, doc_type, extracted_data, raw_text):
        """Analyze patterns to suggest improvements"""
        if doc_type not in self.document_types:
            return
        
        # This is a simplified pattern analysis
        # In a real implementation, you might use ML techniques
        for field, value in extracted_data.items():
            if field not in self.learning_data['pattern_improvements']:
                self.learning_data['pattern_improvements'][field] = []
            
            # Store successful extractions for pattern refinement
            self.learning_data['pattern_improvements'][field].append({
                'value': value,
                'context': raw_text[:200],  # Store context
                'doc_type': doc_type
            })
    
    def get_all_types(self):
        """Get all available document types"""
        return list(self.document_types.keys())
    
    def get_type_config(self, doc_type):
        """Get configuration for a specific document type"""
        return self.document_types.get(doc_type, {})
    
    def suggest_document_type(self, text):
        """Suggest possible document types for unknown documents"""
        doc_type, confidence = self.identify_document_type(text)
        
        suggestions = []
        if doc_type == 'Unknown':
            # Provide suggestions based on partial matches
            text_lower = text.lower()
            for dtype, config in self.document_types.items():
                partial_score = 0
                for keyword in config['keywords']:
                    if keyword in text_lower:
                        partial_score += 1
                
                if partial_score > 0:
                    suggestions.append({
                        'type': dtype,
                        'confidence': partial_score / len(config['keywords']),
                        'reason': f"Found {partial_score} matching keywords"
                    })
        
        return suggestions
