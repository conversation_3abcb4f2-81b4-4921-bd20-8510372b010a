import json
import os
import re
from datetime import datetime

class DocumentTypeManager:
    def __init__(self, config_file='document_types.json'):
        self.config_file = config_file
        self.document_types = self.load_document_types()
        self.learning_data = self.load_learning_data()
    
    def load_document_types(self):
        """Load document type configurations"""
        default_types = {
            'PAN Card': {
                'keywords': ['income tax', 'permanent account', 'pan', 'income tax department', 'govt of india', 'government of india', 'tax', 'department'],
                'patterns': {
                    'PAN Number': r'([A-Z]{5}[0-9]{4}[A-Z])',
                    'Name': r'(?:Name)[:\s]*([A-Za-z\s]+?)(?:\n|Father)',
                    'Father\'s Name': r'(?:Father)[\'s]*\s*(?:Name)[:\s]*([A-Za-z\s]+?)(?:\n|$)',
                    'Date of Birth': r'(?:DOB|Birth)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
                    'Signature': r'(?:Signature)'
                },
                'confidence_threshold': 0.3
            },
            'Aadhaar Card': {
                'keywords': ['aadhaar', 'aadhar', 'unique identification', 'uidai', 'government of india', 'unique identification authority', 'authority of india'],
                'patterns': {
                    'Aadhaar Number': r'(\d{4}\s*\d{4}\s*\d{4})',
                    'Name': r'(?:^|\n)\s*([A-Z][a-z]+(?:\s+[A-Z][a-z]+){1,3})\s*(?:\n|$)',
                    'Date of Birth': r'(?:DOB|Birth)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
                    'Gender': r'\b(male|female|Male|Female)\b',
                    'Address': r'(?:Address)[:\s]*(.+?)(?:\n\n|\n[A-Z]|\d{4}\s*\d{4})',
                    'Father Name': r'(?:Father|S/O)[:\s]*([A-Za-z\s]+)',
                    'Mobile': r'(?:Mobile)[:\s]*(\d{10})'
                },
                'confidence_threshold': 0.3
            },
            'Driving License': {
                'keywords': ['driving license', 'driving licence', 'dl no', 'transport', 'motor vehicle'],
                'patterns': {
                    'License Number': r'(?:DL|License)[:\s]*([A-Z0-9\-]+)',
                    'Name': r'(?:Name)[:\s]*([A-Za-z\s]+?)(?:\n|$)',
                    'Father Name': r'(?:Father|S/O)[:\s]*([A-Za-z\s]+)',
                    'Date of Birth': r'(?:DOB|Birth)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
                    'Issue Date': r'(?:Issue)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
                    'Valid Till': r'(?:Valid)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
                    'Blood Group': r'(?:Blood)[:\s]*([A-Z+-]+)',
                    'Address': r'(?:Address)[:\s]*(.+?)(?:\n\n|\n[A-Z])'
                },
                'confidence_threshold': 0.5
            },
            'Passport': {
                'keywords': ['passport', 'republic of india', 'indian passport', 'ministry of external affairs'],
                'patterns': {
                    'Passport Number': r'([A-Z]\d{7})',
                    'Name': r'(?:Name)[:\s]*([A-Za-z\s]+?)(?:\n|$)',
                    'Surname': r'(?:Surname)[:\s]*([A-Za-z\s]+)',
                    'Given Name': r'(?:Given)[:\s]*([A-Za-z\s]+)',
                    'Date of Birth': r'(?:DOB|Birth)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
                    'Place of Birth': r'(?:Place of Birth)[:\s]*([A-Za-z\s,]+)',
                    'Date of Issue': r'(?:Date of Issue)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
                    'Date of Expiry': r'(?:Date of Expiry)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
                    'Place of Issue': r'(?:Place of Issue)[:\s]*([A-Za-z\s,]+)'
                },
                'confidence_threshold': 0.5
            },
            'Voter ID': {
                'keywords': ['election commission', 'voter', 'epic', 'electoral', 'voter id card'],
                'patterns': {
                    'Voter ID': r'([A-Z]{3}\d{7})',
                    'EPIC No': r'(?:EPIC|No)[:\s]*([A-Z]{3}\d{7})',
                    'Name': r'(?:Name)[:\s]*([A-Za-z\s]+?)(?:\n|$)',
                    'Father Name': r'(?:Father)[:\s]*([A-Za-z\s]+)',
                    'Husband Name': r'(?:Husband)[:\s]*([A-Za-z\s]+)',
                    'Age': r'(?:Age)[:\s]*(\d{1,3})',
                    'Gender': r'(?:Sex|Gender)[:\s]*(Male|Female)',
                    'Address': r'(?:Address)[:\s]*(.+?)(?:\n\n|\n[A-Z])'
                },
                'confidence_threshold': 0.5
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    loaded_types = json.load(f)
                    # Merge with defaults
                    default_types.update(loaded_types)
            except Exception as e:
                print(f"Error loading document types: {e}")
        
        return default_types
    
    def load_learning_data(self):
        """Load learning data for document recognition improvement"""
        learning_file = 'learning_data.json'
        if os.path.exists(learning_file):
            try:
                with open(learning_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading learning data: {e}")
        
        return {'document_samples': [], 'pattern_improvements': {}}
    
    def save_document_types(self):
        """Save document type configurations"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.document_types, f, indent=2)
        except Exception as e:
            print(f"Error saving document types: {e}")
    
    def save_learning_data(self):
        """Save learning data"""
        try:
            with open('learning_data.json', 'w') as f:
                json.dump(self.learning_data, f, indent=2)
        except Exception as e:
            print(f"Error saving learning data: {e}")
    
    def identify_document_type(self, text):
        """Identify document type with confidence scoring"""
        text_lower = text.lower()
        scores = {}

        for doc_type, config in self.document_types.items():
            score = 0
            keyword_matches = 0
            pattern_matches = 0

            # Check keywords
            for keyword in config['keywords']:
                if keyword in text_lower:
                    keyword_matches += 1

            # Check patterns
            for field, pattern in config['patterns'].items():
                if re.search(pattern, text, re.IGNORECASE):
                    pattern_matches += 1

            # Calculate score with improved logic
            keyword_score = keyword_matches / len(config['keywords']) if config['keywords'] else 0
            pattern_score = pattern_matches / len(config['patterns']) if config['patterns'] else 0

            # If we have at least 2 keyword matches OR 1 pattern match, consider it a good candidate
            if keyword_matches >= 2 or pattern_matches >= 1:
                # Weighted average (keywords 50%, patterns 50%)
                score = (keyword_score * 0.5) + (pattern_score * 0.5)
            else:
                score = 0

            scores[doc_type] = score

            # Debug output
            if keyword_matches > 0 or pattern_matches > 0:
                print(f"Debug {doc_type}: keywords={keyword_matches}/{len(config['keywords'])}, patterns={pattern_matches}/{len(config['patterns'])}, score={score:.2f}")

        # Find best match
        best_type = max(scores, key=scores.get) if scores else 'Unknown'
        best_score = scores.get(best_type, 0)

        # Lower threshold for better detection
        threshold = min(0.3, self.document_types.get(best_type, {}).get('confidence_threshold', 0.5))
        if best_score >= threshold:
            return best_type, best_score

        return 'Unknown', 0
    
    def extract_structured_data(self, text, doc_type):
        """Extract structured data using configured patterns"""
        if doc_type not in self.document_types:
            return {}
        
        data = {}
        patterns = self.document_types[doc_type]['patterns']
        
        for field, pattern in patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
            if matches:
                # Take the first match, clean it up
                value = matches[0].strip() if isinstance(matches[0], str) else matches[0]
                if value:
                    data[field] = value
        
        return data
    
    def add_new_type(self, type_name, keywords, patterns, confidence_threshold=0.6):
        """Add a new document type"""
        self.document_types[type_name] = {
            'keywords': keywords,
            'patterns': patterns,
            'confidence_threshold': confidence_threshold,
            'created_date': datetime.now().isoformat()
        }
        self.save_document_types()
    
    def learn_from_document(self, filename, doc_type, extracted_data, raw_text):
        """Learn from a processed document to improve recognition"""
        sample = {
            'filename': filename,
            'document_type': doc_type,
            'extracted_data': extracted_data,
            'raw_text': raw_text,
            'timestamp': datetime.now().isoformat()
        }
        
        self.learning_data['document_samples'].append(sample)
        
        # Analyze patterns for improvement
        self._analyze_patterns(doc_type, extracted_data, raw_text)
        
        self.save_learning_data()
    
    def _analyze_patterns(self, doc_type, extracted_data, raw_text):
        """Analyze patterns to suggest improvements"""
        if doc_type not in self.document_types:
            return
        
        # This is a simplified pattern analysis
        # In a real implementation, you might use ML techniques
        for field, value in extracted_data.items():
            if field not in self.learning_data['pattern_improvements']:
                self.learning_data['pattern_improvements'][field] = []
            
            # Store successful extractions for pattern refinement
            self.learning_data['pattern_improvements'][field].append({
                'value': value,
                'context': raw_text[:200],  # Store context
                'doc_type': doc_type
            })
    
    def get_all_types(self):
        """Get all available document types"""
        return list(self.document_types.keys())
    
    def get_type_config(self, doc_type):
        """Get configuration for a specific document type"""
        return self.document_types.get(doc_type, {})
    
    def suggest_document_type(self, text):
        """Suggest possible document types for unknown documents"""
        doc_type, confidence = self.identify_document_type(text)
        
        suggestions = []
        if doc_type == 'Unknown':
            # Provide suggestions based on partial matches
            text_lower = text.lower()
            for dtype, config in self.document_types.items():
                partial_score = 0
                for keyword in config['keywords']:
                    if keyword in text_lower:
                        partial_score += 1
                
                if partial_score > 0:
                    suggestions.append({
                        'type': dtype,
                        'confidence': partial_score / len(config['keywords']),
                        'reason': f"Found {partial_score} matching keywords"
                    })
        
        return suggestions
