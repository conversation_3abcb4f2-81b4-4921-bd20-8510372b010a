#!/usr/bin/env python3
"""
Test script for the Document OCR Extraction System
"""

import os
import sys
import json
from PIL import Image
import numpy as np

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import flask
        print("✓ Flask imported successfully")
    except ImportError as e:
        print(f"✗ Flask import failed: {e}")
        return False
    
    try:
        import cv2
        print("✓ OpenCV imported successfully")
    except ImportError as e:
        print(f"✗ OpenCV import failed: {e}")
        return False
    
    try:
        import pytesseract
        print("✓ Pytesseract imported successfully")
    except ImportError as e:
        print(f"✗ Pytesseract import failed: {e}")
        return False
    
    try:
        from document_processor import DocumentProcessor
        print("✓ DocumentProcessor imported successfully")
    except ImportError as e:
        print(f"✗ DocumentProcessor import failed: {e}")
        return False
    
    try:
        from document_types import DocumentTypeManager
        print("✓ DocumentTypeManager imported successfully")
    except ImportError as e:
        print(f"✗ DocumentTypeManager import failed: {e}")
        return False
    
    return True

def test_tesseract():
    """Test if Tesseract OCR is working"""
    print("\nTesting Tesseract OCR...")
    
    try:
        import pytesseract
        
        # Create a simple test image with text
        img = Image.new('RGB', (200, 50), color='white')
        
        # Try to extract text (should return empty or minimal text)
        text = pytesseract.image_to_string(img)
        print("✓ Tesseract OCR is working")
        return True
        
    except Exception as e:
        print(f"✗ Tesseract OCR test failed: {e}")
        print("Note: Make sure Tesseract is installed and in your PATH")
        return False

def test_document_processor():
    """Test the document processor functionality"""
    print("\nTesting Document Processor...")
    
    try:
        from document_processor import DocumentProcessor
        
        processor = DocumentProcessor()
        print("✓ DocumentProcessor initialized successfully")
        
        # Test document type identification
        test_text = "INCOME TAX DEPARTMENT PERMANENT ACCOUNT NUMBER **********"
        doc_type = processor.identify_document_type(test_text)
        print(f"✓ Document type identification working: {doc_type}")
        
        return True
        
    except Exception as e:
        print(f"✗ Document processor test failed: {e}")
        return False

def test_document_types():
    """Test the document types manager"""
    print("\nTesting Document Types Manager...")
    
    try:
        from document_types import DocumentTypeManager
        
        manager = DocumentTypeManager()
        print("✓ DocumentTypeManager initialized successfully")
        
        # Test getting document types
        types = manager.get_all_types()
        print(f"✓ Available document types: {types}")
        
        return True
        
    except Exception as e:
        print(f"✗ Document types manager test failed: {e}")
        return False

def test_flask_app():
    """Test if the Flask app can be imported and configured"""
    print("\nTesting Flask Application...")
    
    try:
        from app import app
        print("✓ Flask app imported successfully")
        
        # Test if app is configured
        print(f"✓ Upload folder: {app.config.get('UPLOAD_FOLDER')}")
        print(f"✓ Max content length: {app.config.get('MAX_CONTENT_LENGTH')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Flask app test failed: {e}")
        return False

def create_sample_json():
    """Create a sample JSON output to demonstrate the format"""
    print("\nCreating sample JSON output...")
    
    sample_data = [
        {
            "John Doe": {
                "documents": [
                    {
                        "filename": "john_pan.jpg",
                        "Document Type": "PAN Card",
                        "PAN Number": "**********",
                        "Name": "John Doe",
                        "Father's Name": "Jane Doe",
                        "Date of Birth": "01/01/1990"
                    },
                    {
                        "filename": "john_aadhaar.jpg",
                        "Document Type": "Aadhaar Card",
                        "Aadhaar Number": "1234 5678 9012",
                        "Name": "John Doe",
                        "Date of Birth": "01/01/1990",
                        "Gender": "Male"
                    }
                ]
            }
        },
        {
            "Unknown": {
                "documents": [
                    {
                        "filename": "unknown_doc.jpg",
                        "Document Type": "Unknown",
                        "Raw Text": "Some unrecognized text from document"
                    }
                ]
            }
        }
    ]
    
    try:
        with open('sample_output.json', 'w') as f:
            json.dump(sample_data, f, indent=2)
        print("✓ Sample JSON output created: sample_output.json")
        return True
    except Exception as e:
        print(f"✗ Failed to create sample JSON: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("Document OCR Extraction System - Test Suite")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_tesseract,
        test_document_processor,
        test_document_types,
        test_flask_app,
        create_sample_json
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The application is ready to use.")
        print("\nTo start the application, run:")
        print("python app.py")
    else:
        print("✗ Some tests failed. Please check the error messages above.")
        print("\nCommon issues:")
        print("- Install missing dependencies: pip install -r requirements.txt")
        print("- Install Tesseract OCR: https://github.com/UB-Mannheim/tesseract/wiki")
        print("- Check file permissions and paths")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
